import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Browser automation service URL - environment aware
const getBrowserAutomationServiceUrl = () => {
  // Check if explicitly set in environment
  if (process.env.BROWSER_AUTOMATION_SERVICE_URL) {
    return process.env.BROWSER_AUTOMATION_SERVICE_URL;
  }

  // Auto-detect based on environment
  if (process.env.NODE_ENV === 'production') {
    // In production, browser service runs on same server but different port
    // Use internal server communication (127.0.0.1 for same-server communication)
    return 'http://127.0.0.1:8000';
  }

  // Development default
  return 'http://localhost:8000';
};

const BROWSER_AUTOMATION_SERVICE_URL = getBrowserAutomationServiceUrl();

export async function POST(request: NextRequest) {
  try {
    let user = null;
    let body = await request.json();

    // Check if auth bypass is enabled for development
    if (process.env.NODE_ENV === 'development' && process.env.BYPASS_AUTH_MIDDLEWARE === 'true') {
      console.log('Browser Automation: Bypassing auth checks due to BYPASS_AUTH_MIDDLEWARE=true');
      // For development bypass, we need to extract user_id from request body
      if (body._internal_user_id) {
        user = { id: body._internal_user_id };
      } else {
        return NextResponse.json({ error: 'User ID required when auth bypass is enabled' }, { status: 400 });
      }
    } else {
      // Normal authentication flow
      const cookieStore = await cookies();
      const authCookie = cookieStore.get('sb-access-token');

      if (!authCookie) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }

      // Get user from auth cookie
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser(authCookie.value);
      if (authError || !authUser) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      user = authUser;
    }
    const { task, task_type = 'navigation', config_id, extracted_parameters = {}, user_tier, stream = false } = body;

    if (!task || !config_id) {
      return NextResponse.json({ error: 'Task and config_id are required' }, { status: 400 });
    }

    // Verify the config belongs to the user and has browser automation enabled
    const { data: config, error: configError } = await supabase
      .from('custom_api_configs')
      .select('id, user_id, browser_automation_enabled, name')
      .eq('id', config_id)
      .eq('user_id', user.id)
      .single();

    if (configError || !config) {
      return NextResponse.json({ error: 'Configuration not found' }, { status: 404 });
    }

    if (!config.browser_automation_enabled) {
      return NextResponse.json({ error: 'Browser automation is not enabled for this configuration' }, { status: 403 });
    }

    // Get user tier and validate access
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('tier')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    const userTier = subscription?.tier || 'free';

    // Allow starter, professional, and enterprise tiers
    const allowedTiers = ['starter', 'professional', 'enterprise'];
    if (!allowedTiers.includes(userTier)) {
      return NextResponse.json({ error: 'Browser automation requires Starter plan or higher' }, { status: 403 });
    }

    // Check and enforce quota limits (skip in development bypass mode)
    let usage: any = null;
    let currentMonth: string = '';

    if (!(process.env.NODE_ENV === 'development' && process.env.BYPASS_AUTH_MIDDLEWARE === 'true')) {
      currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

      // Get or create usage record for current month
      const { data: usageData, error: usageError } = await supabase
        .from('browser_automation_usage')
        .select('*')
        .eq('user_id', user.id)
        .eq('month_year', currentMonth)
        .single();

      usage = usageData;

      if (usageError && usageError.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Error checking browser automation usage:', usageError);
        return NextResponse.json({ error: 'Failed to check usage quota' }, { status: 500 });
      }

      // Define tier limits according to Milestone 2 specification
      const tierLimits = {
        free: 0,
        starter: 15,        // Basic browsing (15 tasks/month, complex workflows)
        professional: -1,   // Advanced browsing (unlimited, complex workflows)
        enterprise: -1      // Custom workflows + parallel browsing (unlimited)
      };

      const monthlyLimit = tierLimits[userTier as keyof typeof tierLimits] || 0;

      if (!usage) {
        // Create new usage record
        const { data: newUsage, error: createError } = await supabase
          .from('browser_automation_usage')
          .insert({
            user_id: user.id,
            month_year: currentMonth,
            tier: userTier,
            tasks_used: 0,
            tasks_limit: monthlyLimit
          })
          .select()
          .single();

        if (createError) {
          console.error('Error creating usage record:', createError);
          return NextResponse.json({ error: 'Failed to initialize usage tracking' }, { status: 500 });
        }
        usage = newUsage;
      }

      // Check if user has exceeded quota (skip check for unlimited tiers)
      if (usage.tasks_limit !== -1 && usage.tasks_used >= usage.tasks_limit) {
        return NextResponse.json({
          error: `Monthly browser automation quota exceeded. You have used ${usage.tasks_used}/${usage.tasks_limit} tasks this month.`,
          quota_exceeded: true,
          tasks_used: usage.tasks_used,
          tasks_limit: usage.tasks_limit,
          upgrade_required: userTier === 'starter' ? 'professional' : null
        }, { status: 429 });
      }
    }

    // Get user's API keys and roles for this config
    const { data: apiKeys, error: keysError } = await supabase
      .from('api_keys')
      .select(`
        id,
        provider,
        predefined_model_id,
        temperature,
        label,
        encrypted_api_key,
        api_key_role_assignments (
          role_name
        )
      `)
      .eq('custom_api_config_id', config_id)
      .eq('status', 'active');

    if (keysError || !apiKeys || apiKeys.length === 0) {
      return NextResponse.json({ error: 'No active API keys found for this configuration' }, { status: 400 });
    }

    // Intelligent role selection for browser automation
    // Instead of requiring specific role names, use Gemini classification to determine
    // which of the user's available API keys/roles are suitable for browser automation
    console.log(`[Browser Automation] Found ${apiKeys.length} API keys, using intelligent role selection`);

    // All API keys are potentially usable - let the browser automation service's
    // Gemini classifier determine the best roles for the specific task
    // This provides flexibility and doesn't require rigid role name matching

    // Prepare the request for the browser automation service
    // This integrates with RouKey's BYOK system - user's API keys and role assignments
    const browserAutomationRequest = {
      task,
      task_type,
      user_id: user.id,
      config_id,
      config_name: config.name,
      user_tier: userTier,
      extracted_parameters,
      // Pass user's BYOK API keys with role assignments for intelligent routing
      api_keys: apiKeys.map(key => ({
        id: key.id,
        provider: key.provider,
        model: key.predefined_model_id,
        api_key: key.encrypted_api_key, // User's actual API key from BYOK
        temperature: key.temperature,
        label: key.label,
        roles: key.api_key_role_assignments?.map((assignment: any) => assignment.role_name) || [],
        // Browser automation will use these keys for LLM calls via role routing
        routing_strategy: config.routing_strategy
      })),
      // Browser configuration options (user configurable)
      browser_headless: extracted_parameters.headless ?? true,
      browser_viewport_width: extracted_parameters.viewport_width ?? 1920,
      browser_viewport_height: extracted_parameters.viewport_height ?? 1080,
      browser_slow_mo: extracted_parameters.slow_mo ?? 0,
      browser_devtools: extracted_parameters.devtools ?? false,
      stream
    };

    // Call the browser automation service
    const endpoint = stream ? '/api/v1/browser/execute/stream' : '/api/v1/browser/execute';
    const response = await fetch(`${BROWSER_AUTOMATION_SERVICE_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(browserAutomationRequest)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Browser automation service error' }));
      return NextResponse.json(
        { error: errorData.error || 'Browser automation service failed' },
        { status: response.status }
      );
    }

    // If streaming, return the stream
    if (stream) {
      return new NextResponse(response.body, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    }

    // For non-streaming, return the JSON response
    const result = await response.json();

    // Consume quota after successful execution (only for limited tiers) - skip in development bypass mode
    if (result.success !== false && !(process.env.NODE_ENV === 'development' && process.env.BYPASS_AUTH_MIDDLEWARE === 'true')) {
      try {
        // Only increment usage for limited tiers (not unlimited)
        if (usage.tasks_limit !== -1) {
          await supabase
            .from('browser_automation_usage')
            .update({
              tasks_used: usage.tasks_used + 1,
              last_task_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq('user_id', user.id)
            .eq('month_year', currentMonth);
        } else {
          // For unlimited tiers, just update last_task_at
          await supabase
            .from('browser_automation_usage')
            .update({
              last_task_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq('user_id', user.id)
            .eq('month_year', currentMonth);
        }

        // Log analytics
        await supabase
          .from('browser_automation_analytics')
          .insert({
            user_id: user.id,
            task_id: result.task_id,
            event_type: 'task_completed',
            event_data: {
              task_type,
              extracted_parameters,
              execution_time_ms: result.execution_metadata?.execution_time_seconds ? result.execution_metadata.execution_time_seconds * 1000 : null,
              steps_completed: result.execution_metadata?.steps_completed
            },
            tier: userTier,
            config_id,
            success: result.success,
            execution_time_ms: result.execution_metadata?.execution_time_seconds ? result.execution_metadata.execution_time_seconds * 1000 : null
          });

        const displayUsage = usage.tasks_limit === -1 ? 'unlimited' : `${usage.tasks_used + (usage.tasks_limit !== -1 ? 1 : 0)}/${usage.tasks_limit}`;
        console.log(`[Browser Automation] Task completed for user ${user.id}. Usage: ${displayUsage}`);
      } catch (quotaError) {
        console.error('Error updating quota:', quotaError);
        // Don't fail the request if quota update fails
      }
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('Error executing browser automation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
