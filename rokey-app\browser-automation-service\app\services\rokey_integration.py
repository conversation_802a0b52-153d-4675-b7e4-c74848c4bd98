"""
RouKey Integration Service
Handles communication with RouKey's existing systems for role classification and user configs
"""

import httpx
from typing import Dict, List, Any, Optional
from app.core.config import settings
from app.core.logging import LoggerMixin
from app.core.exceptions import RouKeyIntegrationException


class RouKeyIntegration(LoggerMixin):
    """
    Integration service for communicating with RouKey's existing systems
    
    This service:
    1. Gets user configurations and API keys from RouKey
    2. Uses RouKey's Gemini classifier for role determination
    3. Respects RouKey's routing strategies and tier limits
    4. Reports usage and analytics back to RouKey
    """
    
    def __init__(self):
        self.base_url = settings.ROKEY_API_URL
        self.api_secret = settings.ROKEY_API_SECRET
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            headers={
                "Authorization": f"Bearer {self.api_secret}",
                "Content-Type": "application/json"
            },
            timeout=30.0
        )
    
    async def get_user_config(self, user_id: str, config_id: str) -> Dict[str, Any]:
        """
        Get user's custom configuration from RouKey Supabase database

        Returns:
        - User's API keys per role
        - Routing strategy preferences
        - Role configurations
        - Tier information
        """
        try:
            self.log_info(
                "Fetching user configuration from Supabase",
                user_id=user_id,
                config_id=config_id
            )

            # Get configuration from Supabase directly
            from supabase import create_client, Client
            import os

            supabase_url = os.getenv("SUPABASE_URL", "https://hpkzzhpufhbxtxqaugjh.supabase.co")
            supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

            if not supabase_key:
                raise Exception("SUPABASE_SERVICE_ROLE_KEY not configured")

            supabase: Client = create_client(supabase_url, supabase_key)

            # Get custom config with API keys and role assignments
            config_response = supabase.table('custom_api_configs').select(
                'id, name, routing_strategy, browser_automation_enabled, user_id'
            ).eq('id', config_id).eq('user_id', user_id).single().execute()

            if not config_response.data:
                raise Exception(f"Configuration {config_id} not found for user {user_id}")

            config = config_response.data

            # Get API keys with role assignments
            api_keys_response = supabase.table('api_keys').select(
                '''
                id, provider, predefined_model_id, temperature, label, api_key_encrypted,
                api_key_role_assignments (role_name)
                '''
            ).eq('custom_api_config_id', config_id).eq('status', 'active').execute()

            config['api_keys'] = api_keys_response.data or []

            self.log_info(
                "User configuration retrieved from Supabase",
                user_id=user_id,
                config_id=config_id,
                api_keys_count=len(config['api_keys']),
                routing_strategy=config.get('routing_strategy')
            )

            return config

        except Exception as e:
            self.log_error(f"Failed to get user config from Supabase: {e}")
            # Fallback to mock configuration in debug mode
            if settings.DEBUG:
                self.log_info("Using mock configuration due to database error")
                return await self._get_mock_user_config(user_id, config_id)
            raise RouKeyIntegrationException(f"Failed to get user configuration: {e}")

    async def get_user_subscription(self, user_id: str) -> Dict[str, Any]:
        """Get user's subscription tier from Supabase"""
        try:
            from supabase import create_client, Client
            import os

            supabase_url = os.getenv("SUPABASE_URL", "https://hpkzzhpufhbxtxqaugjh.supabase.co")
            supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

            if not supabase_key:
                raise Exception("SUPABASE_SERVICE_ROLE_KEY not configured")

            supabase: Client = create_client(supabase_url, supabase_key)

            # Get user subscription from the correct table
            subscription_response = supabase.table('subscriptions').select(
                'tier, status'
            ).eq('user_id', user_id).eq('status', 'active').single().execute()

            if subscription_response.data:
                return subscription_response.data

            # Fallback: check user metadata for plan
            user_response = supabase.auth.admin.get_user_by_id(user_id)
            if user_response.user and user_response.user.user_metadata:
                plan = user_response.user.user_metadata.get('plan', 'free')
                return {'tier': plan, 'status': 'active'}

            return {'tier': 'free', 'status': 'active'}

        except Exception as e:
            self.log_error(f"Failed to get user subscription: {e}")
            return {'tier': 'free', 'status': 'active'}

    async def get_user_monthly_browser_usage(self, user_id: str) -> int:
        """Get user's monthly browser automation usage"""
        try:
            # TODO: Implement actual usage tracking from RouKey database
            # For now, return 0 to allow testing
            return 0
        except Exception as e:
            self.log_error(f"Failed to get user usage: {e}")
            return 0

    async def classify_task_roles(self, task: str, user_config: Dict[str, Any], api_keys: List[Dict[str, Any]] = None) -> List[str]:
        """
        Use RouKey's BYOK system to determine required roles for the task
        Integrates with existing intelligent role routing using user's API keys

        Args:
            task: The user's task description
            user_config: User's configuration with available roles
            api_keys: User's BYOK API keys with role assignments

        Returns:
            List of role names that should handle this task
        """
        try:
            self.log_info("Classifying task roles using RouKey's Gemini API", task=task[:100])

            # Extract available roles from user's API key configurations (BYOK system)
            available_roles = []
            available_api_keys = []

            if api_keys:
                for api_key in api_keys:
                    roles = api_key.get("roles", [])
                    available_roles.extend(roles)
                    available_api_keys.append(api_key)

                # Remove duplicates while preserving order
                available_roles = list(dict.fromkeys(available_roles))

            # If no BYOK roles, use roles from user config
            if not available_roles:
                available_roles = list(user_config.get("roles", {}).keys())

            # Intelligent fallback: If no specific roles are configured,
            # we can still use available API keys with general classification
            if not available_roles and available_api_keys:
                self.log_info("No specific roles configured, using intelligent API key selection for browser automation")
                # Use general role names that can be intelligently mapped
                available_roles = ["general_automation", "web_task", "browser_task"]

            if not available_roles and not available_api_keys:
                raise Exception("No API keys or roles available for task classification. User must configure API keys in their BYOK setup.")

            self.log_info(
                "Available roles for classification",
                available_roles=available_roles,
                task=task[:100]
            )

            # Intelligent role selection: Instead of calling a non-existent API endpoint,
            # use smart logic to determine which roles can handle browser automation

            # For browser automation tasks, we can intelligently use any available roles
            # The key insight: browser automation is just another type of task that can be
            # handled by general-purpose LLMs with appropriate prompting

            suitable_roles = []

            # Priority order for browser automation tasks:
            browser_specific_roles = [
                'browser_automation', 'web_automation', 'web_navigator', 'data_extractor',
                'form_filler', 'verification_agent', 'research_assistant', 'task_executor'
            ]

            # Check for browser-specific roles first
            for role in browser_specific_roles:
                if role in available_roles:
                    suitable_roles.append(role)

            # Intelligent fallback: Use general-purpose roles that can handle browser tasks
            general_purpose_roles = [
                'research_synthesis', 'data_extraction_structuring', 'general_chat',
                'logic_reasoning', 'writing'  # These can all handle browser automation with proper prompting
            ]

            if not suitable_roles:
                for role in general_purpose_roles:
                    if role in available_roles:
                        suitable_roles.append(role)

            # Ultimate fallback: Use any available role (they can all potentially handle browser tasks)
            if not suitable_roles and available_roles:
                suitable_roles = available_roles[:3]  # Use up to 3 roles for variety
                self.log_info(f"Using general roles for browser automation: {suitable_roles}")

            if not suitable_roles:
                # This should not happen due to our earlier checks, but just in case
                suitable_roles = ["general_automation"]  # Fallback role name

            self.log_info(
                "Intelligent role selection completed for browser automation",
                task=task[:100],
                available_roles=available_roles,
                selected_roles=suitable_roles,
                reasoning="Used intelligent role selection with browser-specific priority and general-purpose fallback"
            )

            return suitable_roles

        except Exception as e:
            self.log_error(f"Unexpected error in role classification: {e}")
            # Provide intelligent fallback even on errors
            fallback_roles = ["general_chat"] if available_roles and "general_chat" in available_roles else (available_roles[:1] if available_roles else ["general_automation"])
            self.log_info(f"Using fallback roles due to error: {fallback_roles}")
            return fallback_roles
    
    async def get_llm_for_role(
        self, 
        role: str, 
        user_config: Dict[str, Any], 
        routing_strategy: str
    ) -> Dict[str, Any]:
        """
        Get the appropriate LLM configuration for a role based on routing strategy
        
        Args:
            role: The role name
            user_config: User's configuration
            routing_strategy: RouKey's routing strategy (complex_routing, strict_fallback, etc.)
            
        Returns:
            LLM configuration with API key, model, and parameters
        """
        try:
            roles_config = user_config.get("roles", {})
            role_config = roles_config.get(role)
            
            if not role_config:
                # Fallback to general_chat or first available role
                fallback_role = "general_chat"
                if fallback_role not in roles_config and roles_config:
                    fallback_role = list(roles_config.keys())[0]
                role_config = roles_config.get(fallback_role, {})
            
            # Apply routing strategy
            llm_config = await self._apply_routing_strategy(
                role_config, 
                routing_strategy, 
                user_config
            )
            
            self.log_info(
                "LLM configuration retrieved",
                role=role,
                routing_strategy=routing_strategy,
                provider=llm_config.get("provider"),
                model=llm_config.get("model")
            )
            
            return llm_config
            
        except Exception as e:
            self.log_error(f"Failed to get LLM for role {role}: {e}")
            raise RouKeyIntegrationException(f"LLM configuration error: {e}")
    
    async def report_usage(
        self, 
        user_id: str, 
        task_id: str, 
        usage_data: Dict[str, Any]
    ):
        """Report usage statistics back to RouKey for billing and analytics"""
        try:
            # Production implementation: Always report to RouKey API
            try:
                await self.client.post(
                    "/api/analytics/browser-automation-usage",
                    json={
                        "user_id": user_id,
                        "task_id": task_id,
                        "timestamp": usage_data.get("timestamp"),
                        "tokens_used": usage_data.get("tokens_used", 0),
                        "steps_completed": usage_data.get("steps_completed", 0),
                        "execution_time_ms": usage_data.get("execution_time_ms", 0),
                        "success": usage_data.get("success", False),
                        "roles_used": usage_data.get("roles_used", []),
                        "routing_strategy": usage_data.get("routing_strategy"),
                        "user_tier": usage_data.get("user_tier")
                    }
                )

                self.log_info("Usage reported to RouKey API", user_id=user_id, task_id=task_id)

            except httpx.HTTPError as api_error:
                self.log_error(f"Failed to report usage to RouKey API: {api_error}")
                # In debug mode, log mock usage reporting
                if settings.DEBUG:
                    self.log_info("Mock usage reporting (API unavailable)", user_id=user_id, usage_data=usage_data)
            
        except Exception as e:
            self.log_error(f"Failed to report usage: {e}")
            # Don't raise exception - usage reporting shouldn't break the flow
    
    async def _apply_routing_strategy(
        self, 
        role_config: Dict[str, Any], 
        strategy: str, 
        user_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Apply RouKey's routing strategy to select appropriate LLM"""
        
        api_keys = role_config.get("api_keys", [])
        if not api_keys:
            raise RouKeyIntegrationException("No API keys configured for role")
        
        if strategy == "complex_routing":
            # Use complexity-based routing (would need task complexity analysis)
            # For now, use first available key
            selected_key = api_keys[0]
        elif strategy == "strict_fallback":
            # Use primary key, fallback on failure
            selected_key = api_keys[0]  # Primary
        elif strategy == "ab_testing":
            # A/B test between available keys
            import random
            selected_key = random.choice(api_keys)
        else:
            # Default routing
            selected_key = api_keys[0]
        
        return {
            "api_key": selected_key.get("key"),
            "provider": selected_key.get("provider"),
            "model": selected_key.get("model"),
            "temperature": role_config.get("temperature", 0.7),
            "max_tokens": role_config.get("max_tokens", 2000)
        }
    
    # REMOVED: No mock configurations in commercial app - all data must come from RouKey API
    
    # REMOVED: No mock classification in commercial app - all classification must use RouKey's Gemini API
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
