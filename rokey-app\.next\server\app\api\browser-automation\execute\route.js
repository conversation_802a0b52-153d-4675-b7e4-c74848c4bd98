/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/browser-automation/execute/route";
exports.ids = ["app/api/browser-automation/execute/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&page=%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbrowser-automation%2Fexecute%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&page=%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbrowser-automation%2Fexecute%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_browser_automation_execute_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/browser-automation/execute/route.ts */ \"(rsc)/./src/app/api/browser-automation/execute/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/browser-automation/execute/route\",\n        pathname: \"/api/browser-automation/execute\",\n        filename: \"route\",\n        bundlePath: \"app/api/browser-automation/execute/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\browser-automation\\\\execute\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_browser_automation_execute_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&page=%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbrowser-automation%2Fexecute%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/browser-automation/execute/route.ts":
/*!*********************************************************!*\
  !*** ./src/app/api/browser-automation/execute/route.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\nconst supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n// Browser automation service URL - environment aware\nconst getBrowserAutomationServiceUrl = ()=>{\n    // Check if explicitly set in environment\n    if (process.env.BROWSER_AUTOMATION_SERVICE_URL) {\n        return process.env.BROWSER_AUTOMATION_SERVICE_URL;\n    }\n    // Auto-detect based on environment\n    if (false) {}\n    // Development default\n    return 'http://localhost:8000';\n};\nconst BROWSER_AUTOMATION_SERVICE_URL = getBrowserAutomationServiceUrl();\nasync function POST(request) {\n    try {\n        let user = null;\n        let body = await request.json();\n        // Check if auth bypass is enabled for development\n        if ( true && process.env.BYPASS_AUTH_MIDDLEWARE === 'true') {\n            console.log('Browser Automation: Bypassing auth checks due to BYPASS_AUTH_MIDDLEWARE=true');\n            // For development bypass, we need to extract user_id from request body\n            if (body._internal_user_id) {\n                user = {\n                    id: body._internal_user_id\n                };\n            } else {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'User ID required when auth bypass is enabled'\n                }, {\n                    status: 400\n                });\n            }\n        } else {\n            // Normal authentication flow\n            const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n            const authCookie = cookieStore.get('sb-access-token');\n            if (!authCookie) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Unauthorized'\n                }, {\n                    status: 401\n                });\n            }\n            // Get user from auth cookie\n            const { data: { user: authUser }, error: authError } = await supabase.auth.getUser(authCookie.value);\n            if (authError || !authUser) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Unauthorized'\n                }, {\n                    status: 401\n                });\n            }\n            user = authUser;\n        }\n        const { task, task_type = 'navigation', config_id, extracted_parameters = {}, user_tier, stream = false } = body;\n        if (!task || !config_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Task and config_id are required'\n            }, {\n                status: 400\n            });\n        }\n        // Verify the config belongs to the user and has browser automation enabled\n        const { data: config, error: configError } = await supabase.from('custom_api_configs').select('id, user_id, browser_automation_enabled, name').eq('id', config_id).eq('user_id', user.id).single();\n        if (configError || !config) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Configuration not found'\n            }, {\n                status: 404\n            });\n        }\n        if (!config.browser_automation_enabled) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Browser automation is not enabled for this configuration'\n            }, {\n                status: 403\n            });\n        }\n        // Get user tier and validate access\n        const { data: subscription, error: subError } = await supabase.from('subscriptions').select('tier').eq('user_id', user.id).eq('status', 'active').single();\n        const userTier = subscription?.tier || 'free';\n        // Allow starter, professional, and enterprise tiers\n        const allowedTiers = [\n            'starter',\n            'professional',\n            'enterprise'\n        ];\n        if (!allowedTiers.includes(userTier)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Browser automation requires Starter plan or higher'\n            }, {\n                status: 403\n            });\n        }\n        // Check and enforce quota limits (skip in development bypass mode)\n        let usage = null;\n        let currentMonth = '';\n        if (!( true && process.env.BYPASS_AUTH_MIDDLEWARE === 'true')) {\n            currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format\n            // Get or create usage record for current month\n            const { data: usageData, error: usageError } = await supabase.from('browser_automation_usage').select('*').eq('user_id', user.id).eq('month_year', currentMonth).single();\n            usage = usageData;\n            if (usageError && usageError.code !== 'PGRST116') {\n                console.error('Error checking browser automation usage:', usageError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to check usage quota'\n                }, {\n                    status: 500\n                });\n            }\n            // Define tier limits according to Milestone 2 specification\n            const tierLimits = {\n                free: 0,\n                starter: 15,\n                professional: -1,\n                enterprise: -1 // Custom workflows + parallel browsing (unlimited)\n            };\n            const monthlyLimit = tierLimits[userTier] || 0;\n            if (!usage) {\n                // Create new usage record\n                const { data: newUsage, error: createError } = await supabase.from('browser_automation_usage').insert({\n                    user_id: user.id,\n                    month_year: currentMonth,\n                    tier: userTier,\n                    tasks_used: 0,\n                    tasks_limit: monthlyLimit\n                }).select().single();\n                if (createError) {\n                    console.error('Error creating usage record:', createError);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'Failed to initialize usage tracking'\n                    }, {\n                        status: 500\n                    });\n                }\n                usage = newUsage;\n            }\n            // Check if user has exceeded quota (skip check for unlimited tiers)\n            if (usage.tasks_limit !== -1 && usage.tasks_used >= usage.tasks_limit) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `Monthly browser automation quota exceeded. You have used ${usage.tasks_used}/${usage.tasks_limit} tasks this month.`,\n                    quota_exceeded: true,\n                    tasks_used: usage.tasks_used,\n                    tasks_limit: usage.tasks_limit,\n                    upgrade_required: userTier === 'starter' ? 'professional' : null\n                }, {\n                    status: 429\n                });\n            }\n        }\n        // Get user's API keys and roles for this config\n        const { data: apiKeys, error: keysError } = await supabase.from('api_keys').select(`\n        id,\n        provider,\n        predefined_model_id,\n        temperature,\n        label,\n        api_key_encrypted,\n        key_role_assignments (\n          role_name\n        )\n      `).eq('custom_api_config_id', config_id).eq('status', 'active');\n        if (keysError || !apiKeys || apiKeys.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No active API keys found for this configuration'\n            }, {\n                status: 400\n            });\n        }\n        // Intelligent role selection for browser automation\n        // Instead of requiring specific role names, use Gemini classification to determine\n        // which of the user's available API keys/roles are suitable for browser automation\n        console.log(`[Browser Automation] Found ${apiKeys.length} API keys, using intelligent role selection`);\n        // All API keys are potentially usable - let the browser automation service's\n        // Gemini classifier determine the best roles for the specific task\n        // This provides flexibility and doesn't require rigid role name matching\n        // Prepare the request for the browser automation service\n        // This integrates with RouKey's BYOK system - user's API keys and role assignments\n        const browserAutomationRequest = {\n            task,\n            task_type,\n            user_id: user.id,\n            config_id,\n            config_name: config.name,\n            user_tier: userTier,\n            extracted_parameters,\n            // Pass user's BYOK API keys with role assignments for intelligent routing\n            api_keys: apiKeys.map((key)=>({\n                    id: key.id,\n                    provider: key.provider,\n                    model: key.predefined_model_id,\n                    api_key: key.api_key,\n                    temperature: key.temperature,\n                    label: key.label,\n                    roles: key.key_role_assignments?.map((assignment)=>assignment.role_name) || [],\n                    // Browser automation will use these keys for LLM calls via role routing\n                    routing_strategy: config.routing_strategy\n                })),\n            // Browser configuration options (user configurable)\n            browser_headless: extracted_parameters.headless ?? true,\n            browser_viewport_width: extracted_parameters.viewport_width ?? 1920,\n            browser_viewport_height: extracted_parameters.viewport_height ?? 1080,\n            browser_slow_mo: extracted_parameters.slow_mo ?? 0,\n            browser_devtools: extracted_parameters.devtools ?? false,\n            stream\n        };\n        // Call the browser automation service\n        const endpoint = stream ? '/api/v1/browser/execute/stream' : '/api/v1/browser/execute';\n        const response = await fetch(`${BROWSER_AUTOMATION_SERVICE_URL}${endpoint}`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(browserAutomationRequest)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({\n                    error: 'Browser automation service error'\n                }));\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: errorData.error || 'Browser automation service failed'\n            }, {\n                status: response.status\n            });\n        }\n        // If streaming, return the stream\n        if (stream) {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(response.body, {\n                headers: {\n                    'Content-Type': 'text/event-stream',\n                    'Cache-Control': 'no-cache',\n                    'Connection': 'keep-alive'\n                }\n            });\n        }\n        // For non-streaming, return the JSON response\n        const result = await response.json();\n        // Consume quota after successful execution (only for limited tiers) - skip in development bypass mode\n        if (result.success !== false && !( true && process.env.BYPASS_AUTH_MIDDLEWARE === 'true')) {\n            try {\n                // Only increment usage for limited tiers (not unlimited)\n                if (usage.tasks_limit !== -1) {\n                    await supabase.from('browser_automation_usage').update({\n                        tasks_used: usage.tasks_used + 1,\n                        last_task_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString()\n                    }).eq('user_id', user.id).eq('month_year', currentMonth);\n                } else {\n                    // For unlimited tiers, just update last_task_at\n                    await supabase.from('browser_automation_usage').update({\n                        last_task_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString()\n                    }).eq('user_id', user.id).eq('month_year', currentMonth);\n                }\n                // Log analytics\n                await supabase.from('browser_automation_analytics').insert({\n                    user_id: user.id,\n                    task_id: result.task_id,\n                    event_type: 'task_completed',\n                    event_data: {\n                        task_type,\n                        extracted_parameters,\n                        execution_time_ms: result.execution_metadata?.execution_time_seconds ? result.execution_metadata.execution_time_seconds * 1000 : null,\n                        steps_completed: result.execution_metadata?.steps_completed\n                    },\n                    tier: userTier,\n                    config_id,\n                    success: result.success,\n                    execution_time_ms: result.execution_metadata?.execution_time_seconds ? result.execution_metadata.execution_time_seconds * 1000 : null\n                });\n                const displayUsage = usage.tasks_limit === -1 ? 'unlimited' : `${usage.tasks_used + (usage.tasks_limit !== -1 ? 1 : 0)}/${usage.tasks_limit}`;\n                console.log(`[Browser Automation] Task completed for user ${user.id}. Usage: ${displayUsage}`);\n            } catch (quotaError) {\n                console.error('Error updating quota:', quotaError);\n            // Don't fail the request if quota update fails\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error('Error executing browser automation:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/browser-automation/execute/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&page=%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbrowser-automation%2Fexecute%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();