/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/browser-automation/execute/route";
exports.ids = ["app/api/browser-automation/execute/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&page=%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbrowser-automation%2Fexecute%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&page=%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbrowser-automation%2Fexecute%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_browser_automation_execute_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/browser-automation/execute/route.ts */ \"(rsc)/./src/app/api/browser-automation/execute/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/browser-automation/execute/route\",\n        pathname: \"/api/browser-automation/execute\",\n        filename: \"route\",\n        bundlePath: \"app/api/browser-automation/execute/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\browser-automation\\\\execute\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_browser_automation_execute_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&page=%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbrowser-automation%2Fexecute%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/browser-automation/execute/route.ts":
/*!*********************************************************!*\
  !*** ./src/app/api/browser-automation/execute/route.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\nconst supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n// Browser automation service URL - environment aware\nconst getBrowserAutomationServiceUrl = ()=>{\n    // Check if explicitly set in environment\n    if (process.env.BROWSER_AUTOMATION_SERVICE_URL) {\n        return process.env.BROWSER_AUTOMATION_SERVICE_URL;\n    }\n    // Auto-detect based on environment\n    if (false) {}\n    // Development default\n    return 'http://localhost:8000';\n};\nconst BROWSER_AUTOMATION_SERVICE_URL = getBrowserAutomationServiceUrl();\nasync function POST(request) {\n    try {\n        let user = null;\n        let body = await request.json();\n        // Check if auth bypass is enabled for development\n        if ( true && process.env.BYPASS_AUTH_MIDDLEWARE === 'true') {\n            console.log('Browser Automation: Bypassing auth checks due to BYPASS_AUTH_MIDDLEWARE=true');\n            // For development bypass, we need to extract user_id from request body\n            if (body._internal_user_id) {\n                user = {\n                    id: body._internal_user_id\n                };\n            } else {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'User ID required when auth bypass is enabled'\n                }, {\n                    status: 400\n                });\n            }\n        } else {\n            // Normal authentication flow\n            const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n            const authCookie = cookieStore.get('sb-access-token');\n            if (!authCookie) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Unauthorized'\n                }, {\n                    status: 401\n                });\n            }\n            // Get user from auth cookie\n            const { data: { user: authUser }, error: authError } = await supabase.auth.getUser(authCookie.value);\n            if (authError || !authUser) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Unauthorized'\n                }, {\n                    status: 401\n                });\n            }\n            user = authUser;\n        }\n        const { task, task_type = 'navigation', config_id, extracted_parameters = {}, user_tier, stream = false } = body;\n        if (!task || !config_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Task and config_id are required'\n            }, {\n                status: 400\n            });\n        }\n        // Verify the config belongs to the user and has browser automation enabled\n        const { data: config, error: configError } = await supabase.from('custom_api_configs').select('id, user_id, browser_automation_enabled, name').eq('id', config_id).eq('user_id', user.id).single();\n        if (configError || !config) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Configuration not found'\n            }, {\n                status: 404\n            });\n        }\n        if (!config.browser_automation_enabled) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Browser automation is not enabled for this configuration'\n            }, {\n                status: 403\n            });\n        }\n        // Get user tier and validate access\n        const { data: subscription, error: subError } = await supabase.from('subscriptions').select('tier').eq('user_id', user.id).eq('status', 'active').single();\n        const userTier = subscription?.tier || 'free';\n        // Allow starter, professional, and enterprise tiers\n        const allowedTiers = [\n            'starter',\n            'professional',\n            'enterprise'\n        ];\n        if (!allowedTiers.includes(userTier)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Browser automation requires Starter plan or higher'\n            }, {\n                status: 403\n            });\n        }\n        // Check and enforce quota limits (skip in development bypass mode)\n        let usage = null;\n        let currentMonth = '';\n        if (!( true && process.env.BYPASS_AUTH_MIDDLEWARE === 'true')) {\n            currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format\n            // Get or create usage record for current month\n            const { data: usageData, error: usageError } = await supabase.from('browser_automation_usage').select('*').eq('user_id', user.id).eq('month_year', currentMonth).single();\n            usage = usageData;\n            if (usageError && usageError.code !== 'PGRST116') {\n                console.error('Error checking browser automation usage:', usageError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to check usage quota'\n                }, {\n                    status: 500\n                });\n            }\n            // Define tier limits according to Milestone 2 specification\n            const tierLimits = {\n                free: 0,\n                starter: 15,\n                professional: -1,\n                enterprise: -1 // Custom workflows + parallel browsing (unlimited)\n            };\n            const monthlyLimit = tierLimits[userTier] || 0;\n            if (!usage) {\n                // Create new usage record\n                const { data: newUsage, error: createError } = await supabase.from('browser_automation_usage').insert({\n                    user_id: user.id,\n                    month_year: currentMonth,\n                    tier: userTier,\n                    tasks_used: 0,\n                    tasks_limit: monthlyLimit\n                }).select().single();\n                if (createError) {\n                    console.error('Error creating usage record:', createError);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'Failed to initialize usage tracking'\n                    }, {\n                        status: 500\n                    });\n                }\n                usage = newUsage;\n            }\n            // Check if user has exceeded quota (skip check for unlimited tiers)\n            if (usage.tasks_limit !== -1 && usage.tasks_used >= usage.tasks_limit) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `Monthly browser automation quota exceeded. You have used ${usage.tasks_used}/${usage.tasks_limit} tasks this month.`,\n                    quota_exceeded: true,\n                    tasks_used: usage.tasks_used,\n                    tasks_limit: usage.tasks_limit,\n                    upgrade_required: userTier === 'starter' ? 'professional' : null\n                }, {\n                    status: 429\n                });\n            }\n        }\n        // Get user's API keys and roles for this config\n        const { data: apiKeys, error: keysError } = await supabase.from('api_keys').select(`\n        id,\n        provider,\n        predefined_model_id,\n        temperature,\n        label,\n        encrypted_api_key,\n        api_key_role_assignments (\n          role_name\n        )\n      `).eq('custom_api_config_id', config_id).eq('status', 'active');\n        if (keysError || !apiKeys || apiKeys.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No active API keys found for this configuration'\n            }, {\n                status: 400\n            });\n        }\n        // Intelligent role selection for browser automation\n        // Instead of requiring specific role names, use Gemini classification to determine\n        // which of the user's available API keys/roles are suitable for browser automation\n        console.log(`[Browser Automation] Found ${apiKeys.length} API keys, using intelligent role selection`);\n        // All API keys are potentially usable - let the browser automation service's\n        // Gemini classifier determine the best roles for the specific task\n        // This provides flexibility and doesn't require rigid role name matching\n        // Prepare the request for the browser automation service\n        // This integrates with RouKey's BYOK system - user's API keys and role assignments\n        const browserAutomationRequest = {\n            task,\n            task_type,\n            user_id: user.id,\n            config_id,\n            config_name: config.name,\n            user_tier: userTier,\n            extracted_parameters,\n            // Pass user's BYOK API keys with role assignments for intelligent routing\n            api_keys: apiKeys.map((key)=>({\n                    id: key.id,\n                    provider: key.provider,\n                    model: key.predefined_model_id,\n                    api_key: key.encrypted_api_key,\n                    temperature: key.temperature,\n                    label: key.label,\n                    roles: key.api_key_role_assignments?.map((assignment)=>assignment.role_name) || [],\n                    // Browser automation will use these keys for LLM calls via role routing\n                    routing_strategy: config.routing_strategy\n                })),\n            // Browser configuration options (user configurable)\n            browser_headless: extracted_parameters.headless ?? true,\n            browser_viewport_width: extracted_parameters.viewport_width ?? 1920,\n            browser_viewport_height: extracted_parameters.viewport_height ?? 1080,\n            browser_slow_mo: extracted_parameters.slow_mo ?? 0,\n            browser_devtools: extracted_parameters.devtools ?? false,\n            stream\n        };\n        // Call the browser automation service\n        const endpoint = stream ? '/api/v1/browser/execute/stream' : '/api/v1/browser/execute';\n        console.log(`[Browser Automation Execute] Calling service at: ${BROWSER_AUTOMATION_SERVICE_URL}${endpoint}`);\n        console.log(`[Browser Automation Execute] Request payload:`, JSON.stringify(browserAutomationRequest, null, 2));\n        const response = await fetch(`${BROWSER_AUTOMATION_SERVICE_URL}${endpoint}`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(browserAutomationRequest)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({\n                    error: 'Browser automation service error'\n                }));\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: errorData.error || 'Browser automation service failed'\n            }, {\n                status: response.status\n            });\n        }\n        // If streaming, return the stream\n        if (stream) {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(response.body, {\n                headers: {\n                    'Content-Type': 'text/event-stream',\n                    'Cache-Control': 'no-cache',\n                    'Connection': 'keep-alive'\n                }\n            });\n        }\n        // For non-streaming, return the JSON response\n        const result = await response.json();\n        console.log(`[Browser Automation Execute] Service response received:`, JSON.stringify(result, null, 2));\n        // Consume quota after successful execution (only for limited tiers) - skip in development bypass mode\n        if (result.success !== false && !( true && process.env.BYPASS_AUTH_MIDDLEWARE === 'true')) {\n            try {\n                // Only increment usage for limited tiers (not unlimited)\n                if (usage.tasks_limit !== -1) {\n                    await supabase.from('browser_automation_usage').update({\n                        tasks_used: usage.tasks_used + 1,\n                        last_task_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString()\n                    }).eq('user_id', user.id).eq('month_year', currentMonth);\n                } else {\n                    // For unlimited tiers, just update last_task_at\n                    await supabase.from('browser_automation_usage').update({\n                        last_task_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString()\n                    }).eq('user_id', user.id).eq('month_year', currentMonth);\n                }\n                // Log analytics\n                await supabase.from('browser_automation_analytics').insert({\n                    user_id: user.id,\n                    task_id: result.task_id,\n                    event_type: 'task_completed',\n                    event_data: {\n                        task_type,\n                        extracted_parameters,\n                        execution_time_ms: result.execution_metadata?.execution_time_seconds ? result.execution_metadata.execution_time_seconds * 1000 : null,\n                        steps_completed: result.execution_metadata?.steps_completed\n                    },\n                    tier: userTier,\n                    config_id,\n                    success: result.success,\n                    execution_time_ms: result.execution_metadata?.execution_time_seconds ? result.execution_metadata.execution_time_seconds * 1000 : null\n                });\n                const displayUsage = usage.tasks_limit === -1 ? 'unlimited' : `${usage.tasks_used + (usage.tasks_limit !== -1 ? 1 : 0)}/${usage.tasks_limit}`;\n                console.log(`[Browser Automation] Task completed for user ${user.id}. Usage: ${displayUsage}`);\n            } catch (quotaError) {\n                console.error('Error updating quota:', quotaError);\n            // Don't fail the request if quota update fails\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error('Error executing browser automation:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/browser-automation/execute/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&page=%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbrowser-automation%2Fexecute%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();